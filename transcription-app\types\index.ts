// File upload and processing types
export interface FileUpload {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  uploadedAt: Date;
  completedAt?: Date;
  error?: string;
}

// Transcription job types
export interface TranscriptionJob {
  id: string;
  fileId: string;
  fileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  model: 'gemini-2.5-flash' | 'gemini-2.5-flash-lite';
  language?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  result?: TranscriptionResult;
}

// Transcription result types
export interface TranscriptionResult {
  id: string;
  jobId: string;
  text: string;
  srt?: string;
  vtt?: string;
  segments?: TranscriptionSegment[];
  confidence?: number;
  language?: string;
  duration?: number;
  wordCount?: number;
}

export interface TranscriptionSegment {
  id: string;
  start: number;
  end: number;
  text: string;
  confidence?: number;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UploadResponse extends ApiResponse {
  data?: {
    fileId: string;
    fileName: string;
    size: number;
    uploadUrl?: string;
  };
}

export interface JobStatusResponse extends ApiResponse {
  data?: TranscriptionJob;
}

// Configuration types
export interface AppConfig {
  maxFileSize: number;
  supportedFormats: string[];
  rateLimit: {
    requestsPerHour: number;
    maxFileSize: number;
  };
  gemini: {
    primaryModel: string;
    fallbackModel: string;
    maxRetries: number;
  };
}

// UI component types
export interface ProgressIndicator {
  current: number;
  total: number;
  status: string;
  eta?: number;
}

export interface FileValidationError {
  type: 'size' | 'format' | 'duration' | 'corrupted';
  message: string;
  maxAllowed?: number;
  actual?: number;
}

// Gemini API types
export interface GeminiFile {
  name: string;
  displayName: string;
  mimeType: string;
  sizeBytes: string;
  createTime: string;
  updateTime: string;
  expirationTime: string;
  sha256Hash: string;
  uri: string;
  state: 'PROCESSING' | 'ACTIVE' | 'FAILED';
  error?: {
    code: number;
    message: string;
  };
}

export interface GeminiGenerateContentRequest {
  contents: Array<{
    parts: Array<{
      text?: string;
      fileData?: {
        mimeType: string;
        fileUri: string;
      };
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    topK?: number;
    topP?: number;
    maxOutputTokens?: number;
  };
}

export interface GeminiGenerateContentResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}
