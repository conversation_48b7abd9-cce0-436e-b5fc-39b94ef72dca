---
type: "always_apply"
---

# Development Rules and Implementation Guidelines
*Video and Audio Transcription Web Application*

## MANDATORY PRE-TASK REQUIREMENTS

### 1. COMPREHENSIVE PROJECT REVIEW (REQUIRED BEFORE ANY WORK)
Before initiating ANY task or responding to user requests, you MUST:

1. **Read plan.md completely** - Understand the full project architecture, technology stack, and implementation roadmap
2. **Read progress.md thoroughly** - Review all completed tasks, current status, and identified next steps  
3. **Analyze all existing code files** - Examine current implementations to understand:
   - Functionality already built and working
   - Code patterns, architecture decisions, and conventions
   - Integration points and dependencies
   - Technical debt or areas requiring improvement
   - File structure and organization

### 2. CURRENT RESEARCH REQUIREMENTS (MANDATORY FOR ALL IMPLEMENTATIONS)
For EVERY implementation task, conduct comprehensive web research using queries specifically dated "September 2025":

**Required Research Areas:**
- Latest API documentation and breaking changes
- Current security best practices and vulnerabilities
- Most recent library versions and compatibility matrices
- Updated pricing, rate limits, and quota changes
- New features, capabilities, and performance improvements
- Migration guides and deprecation notices

**Research Query Format:** `"[Technology/API] [specific feature] September 2025 latest updates"`

**Examples:**
- `"Google Gemini API file upload September 2025 latest updates"`
- `"Next.js 15.5 React 19 compatibility September 2025 latest updates"`
- `"BullMQ Redis job queue performance September 2025 latest updates"`

## PROJECT OBJECTIVES AND TECHNICAL SPECIFICATIONS

### Primary Goal
Develop a production-ready video and audio transcription web application with:

**Core Features:**
- File support up to 2GB (MP4, MP3, WAV, M4A, AAC, FLAC, OPUS, PCM, MPEG)
- Multi-tier API strategy: Gemini 2.0 Flash (primary) → Gemini 2.5 Flash (fallback) → Gemini 2.5 Flash-Lite (cost-effective)
- Output formats: SRT, VTT, and plain text transcription
- Real-time progress tracking with WebSocket or Server-Sent Events
- Responsive design optimized for desktop and mobile

**Technical Architecture:**
- Next.js 15.5 with React 19 and TypeScript
- Shadcn/ui with Tailwind CSS for UI components
- BullMQ + Redis for job queue management
- Node.js 22 with enhanced file handling
- Smart caching with Redis 7+ and context caching

### Quality Standards (NON-NEGOTIABLE)
- **Security**: Multi-layer file validation, MIME type verification, and input sanitization
- **Performance**: Optimized for 2GB file handling with concurrent processing
- **Scalability**: Rate limiting, job queues, and horizontal scaling capabilities
- **Cost Efficiency**: Batch processing, smart API routing, and token optimization
- **User Experience**: Intuitive interface with clear progress indicators and error handling

## IMPLEMENTATION RULES (STRICTLY ENFORCED)

### Rule 1: Research-First Development
- **NEVER implement without current research** - All implementations must be based on September 2025 information
- **ALWAYS verify API documentation** - Check for breaking changes, new features, and updated pricing
- **MUST validate security practices** - Research latest vulnerabilities and mitigation strategies
- **REQUIRED to check dependency compatibility** - Ensure all libraries work together without conflicts

### Rule 2: Code Continuity and Architecture Consistency
- **MUST analyze existing patterns** - Study current code structure before adding functionality
- **REQUIRED to maintain conventions** - Follow established naming, file organization, and coding standards
- **MUST ensure seamless integration** - New code must work harmoniously with existing implementations
- **REQUIRED to update related components** - Modify dependent files when making architectural changes

### Rule 3: Comprehensive Documentation and Progress Tracking
- **MUST document all decisions** - Explain technical choices, trade-offs, and reasoning
- **REQUIRED to update progress.md** - After every completed task with detailed summaries
- **MUST include meaningful comments** - Explain complex logic, algorithms, and integration points
- **REQUIRED to note plan deviations** - Document any changes from the original architecture

### Rule 4: Quality Assurance and Testing
- **MUST implement comprehensive error handling** - Cover all failure scenarios with graceful degradation
- **REQUIRED to validate all inputs** - File uploads, API responses, and user data
- **MUST test integration points** - Verify component interactions and data flow
- **REQUIRED to consider security implications** - Assess and mitigate potential vulnerabilities

### Rule 5: Cost and Performance Optimization
- **MUST implement smart API routing** - Use most cost-effective model based on file characteristics
- **REQUIRED to optimize token usage** - Minimize API costs through preprocessing and caching
- **MUST implement efficient caching** - Redis caching, context caching, and result storage
- **REQUIRED to monitor resource consumption** - Track usage patterns and implement limits

## TASK EXECUTION WORKFLOW

### Phase 1: Preparation (MANDATORY - NO EXCEPTIONS)
1. **Complete Project Review**
   - Read plan.md from start to finish
   - Review progress.md for current status and context
   - Analyze all existing code files for patterns and architecture
   - Understand integration points and dependencies

2. **Comprehensive Research**
   - Research specific technologies and APIs for the task
   - Verify current best practices and security standards
   - Check for breaking changes and new features
   - Validate library compatibility and versions

3. **Task Planning**
   - Define specific implementation approach
   - Identify potential challenges and solutions
   - Plan integration with existing code
   - Estimate complexity and dependencies

### Phase 2: Implementation
1. **Follow Established Architecture**
   - Maintain consistency with existing patterns
   - Use established conventions and standards
   - Ensure proper error handling and validation
   - Implement with security and performance as priorities

2. **Integration Testing**
   - Test with existing components
   - Verify data flow and API interactions
   - Validate error scenarios and edge cases
   - Ensure responsive design and accessibility

3. **Code Quality**
   - Write clean, maintainable, and well-commented code
   - Follow TypeScript best practices
   - Implement proper logging and monitoring
   - Optimize for performance and scalability

### Phase 3: Documentation and Handoff (MANDATORY)
1. **Update progress.md** with:
   - Comprehensive task completion summary
   - Files created, modified, or deleted
   - Key technical decisions and reasoning
   - Challenges encountered and solutions implemented
   - Current project status and completion percentage
   - Working features and known issues

2. **Define Next Task**
   - Specific next task with clear requirements
   - Prerequisites and dependencies
   - Estimated complexity and timeline
   - Required research areas and considerations

3. **Integration Documentation**
   - How completed work integrates with existing code
   - Any breaking changes or compatibility issues
   - Testing requirements and validation steps
   - Performance implications and optimizations

## COMMUNICATION STANDARDS

### Response Requirements
- **Begin each response** by confirming you have read plan.md and progress.md
- **Summarize relevant context** from previous work before starting new tasks
- **Provide research-backed justifications** for all technical decisions
- **End with clear next steps** and comprehensive progress updates

### Tone and Approach
- **Instructive**: Provide clear, step-by-step implementation guidance
- **Assertive**: Make definitive technical recommendations based on current research
- **Comprehensive**: Cover all aspects of implementation thoroughly
- **Professional**: Maintain technical accuracy, clarity, and attention to detail

## CRITICAL SUCCESS FACTORS

### Technical Excellence
- All implementations must be production-ready with proper error handling
- Security must be built-in, not added as an afterthought
- Performance optimization must be considered from the start
- Code must be maintainable, scalable, and well-documented

### Project Continuity
- Every task must build upon previous work seamlessly
- Architecture decisions must be consistent throughout the project
- Documentation must be kept current and comprehensive
- Progress tracking must be detailed and actionable

### Cost Efficiency
- API usage must be optimized for minimum cost while maintaining quality
- Caching strategies must be implemented to reduce redundant processing
- Resource consumption must be monitored and controlled
- Batch processing must be used where appropriate for cost savings

These rules ensure consistent, high-quality implementation while maintaining project continuity and achieving the cost-effective, scalable transcription service outlined in the comprehensive plan.
