// File upload constants
export const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024; // 2GB in bytes
export const MAX_STORAGE_PER_PROJECT = 20 * 1024 * 1024 * 1024; // 20GB in bytes
export const FILE_RETENTION_HOURS = 48;

// Supported file formats
export const SUPPORTED_AUDIO_FORMATS = [
  'audio/wav',
  'audio/mp3',
  'audio/mpeg',
  'audio/m4a',
  'audio/aac',
  'audio/flac',
  'audio/opus',
  'audio/pcm',
] as const;

export const SUPPORTED_VIDEO_FORMATS = [
  'video/mp4',
  'video/mov',
  'video/quicktime',
  'video/avi',
  'video/x-flv',
  'video/x-matroska',
  'video/webm',
] as const;

export const SUPPORTED_FORMATS = [
  ...SUPPORTED_AUDIO_FORMATS,
  ...SUPPORTED_VIDEO_FORMATS,
] as const;

// File extensions mapping
export const FORMAT_EXTENSIONS = {
  'audio/wav': ['.wav'],
  'audio/mp3': ['.mp3'],
  'audio/mpeg': ['.mp3', '.mpeg'],
  'audio/m4a': ['.m4a'],
  'audio/aac': ['.aac'],
  'audio/flac': ['.flac'],
  'audio/opus': ['.opus'],
  'audio/pcm': ['.pcm'],
  'video/mp4': ['.mp4'],
  'video/mov': ['.mov'],
  'video/quicktime': ['.mov'],
  'video/avi': ['.avi'],
  'video/x-flv': ['.flv'],
  'video/x-matroska': ['.mkv'],
  'video/webm': ['.webm'],
} as const;

// Gemini API constants
export const GEMINI_MODELS = {
  PRIMARY: 'gemini-2.5-flash',
  FALLBACK: 'gemini-2.5-flash-lite',
} as const;

export const GEMINI_ENDPOINTS = {
  UPLOAD: 'https://generativelanguage.googleapis.com/upload/v1beta/files',
  FILES: 'https://generativelanguage.googleapis.com/v1beta/files',
  GENERATE: 'https://generativelanguage.googleapis.com/v1beta/models',
} as const;

// Token calculation constants (approximate)
export const TOKEN_RATES = {
  AUDIO_TOKENS_PER_SECOND: 25,
  VIDEO_TOKENS_PER_SECOND: 258,
  TEXT_CHARS_PER_TOKEN: 4,
} as const;

// Pricing constants (per 1M tokens)
export const PRICING = {
  'gemini-2.5-flash': {
    input: {
      text: 0.15,
      image: 0.15,
      video: 0.15,
      audio: 0.50,
    },
    output: 2.50,
    contextCaching: {
      text: 0.075,
      image: 0.075,
      video: 0.075,
      audio: 0.25,
    },
  },
  'gemini-2.5-flash-lite': {
    input: {
      text: 0.10,
      image: 0.10,
      video: 0.10,
      audio: 0.30,
    },
    output: 0.40,
    contextCaching: {
      text: 0.025,
      image: 0.025,
      video: 0.025,
      audio: 0.125,
    },
  },
} as const;

// Rate limiting constants
export const RATE_LIMITS = {
  FILES_PER_HOUR: 10,
  MAX_CONCURRENT_JOBS: 3,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
} as const;

// Job status constants
export const JOB_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export const FILE_STATUS = {
  UPLOADING: 'uploading',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error',
} as const;

// Output format constants
export const OUTPUT_FORMATS = {
  TEXT: 'text',
  SRT: 'srt',
  VTT: 'vtt',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds the maximum limit of ${MAX_FILE_SIZE / (1024 * 1024 * 1024)}GB`,
  UNSUPPORTED_FORMAT: 'File format is not supported',
  UPLOAD_FAILED: 'Failed to upload file',
  PROCESSING_FAILED: 'Failed to process file',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later',
  INVALID_FILE: 'Invalid or corrupted file',
  NETWORK_ERROR: 'Network error. Please check your connection',
  API_ERROR: 'API error. Please try again later',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  PROCESSING_STARTED: 'Processing started',
  TRANSCRIPTION_COMPLETED: 'Transcription completed successfully',
} as const;

// UI constants
export const PROGRESS_STEPS = [
  { key: 'upload', label: 'Uploading file' },
  { key: 'validate', label: 'Validating file' },
  { key: 'queue', label: 'Adding to queue' },
  { key: 'process', label: 'Processing transcription' },
  { key: 'complete', label: 'Generating results' },
] as const;

// Development constants
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';

// API routes
export const API_ROUTES = {
  UPLOAD: '/api/upload',
  STATUS: '/api/status',
  DOWNLOAD: '/api/download',
  JOBS: '/api/jobs',
} as const;
