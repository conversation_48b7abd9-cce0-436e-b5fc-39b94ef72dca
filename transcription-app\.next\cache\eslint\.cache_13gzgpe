[{"C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\badge.tsx": "3", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\button.tsx": "4", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\card.tsx": "5", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\progress.tsx": "6", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\lib\\constants.ts": "7", "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\lib\\utils.ts": "8"}, {"size": 689, "mtime": 1757160138981, "results": "9", "hashOfConfig": "10"}, {"size": 4458, "mtime": 1757337715396, "results": "11", "hashOfConfig": "10"}, {"size": 1128, "mtime": 1757336980490, "results": "12", "hashOfConfig": "10"}, {"size": 1901, "mtime": 1757336891040, "results": "13", "hashOfConfig": "10"}, {"size": 1858, "mtime": 1757336979650, "results": "14", "hashOfConfig": "10"}, {"size": 791, "mtime": 1757336980116, "results": "15", "hashOfConfig": "10"}, {"size": 4305, "mtime": 1757336458227, "results": "16", "hashOfConfig": "10"}, {"size": 6283, "mtime": 1757337827617, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1x0go5a", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\OneDrive - UGSM-Monarch Business School GmbH\\Documents\\upload\\transcription-app\\lib\\utils.ts", ["42", "43", "44", "45", "46", "47"], [], {"ruleId": "48", "severity": 2, "message": "49", "line": 59, "column": 48, "nodeType": "50", "messageId": "51", "endLine": 59, "endColumn": 51, "suggestions": "52"}, {"ruleId": "48", "severity": 2, "message": "49", "line": 63, "column": 75, "nodeType": "50", "messageId": "51", "endLine": 63, "endColumn": 78, "suggestions": "53"}, {"ruleId": "48", "severity": 2, "message": "49", "line": 170, "column": 46, "nodeType": "50", "messageId": "51", "endLine": 170, "endColumn": 49, "suggestions": "54"}, {"ruleId": "48", "severity": 2, "message": "49", "line": 170, "column": 56, "nodeType": "50", "messageId": "51", "endLine": 170, "endColumn": 59, "suggestions": "55"}, {"ruleId": "48", "severity": 2, "message": "49", "line": 185, "column": 46, "nodeType": "50", "messageId": "51", "endLine": 185, "endColumn": 49, "suggestions": "56"}, {"ruleId": "48", "severity": 2, "message": "49", "line": 185, "column": 56, "nodeType": "50", "messageId": "51", "endLine": 185, "endColumn": 59, "suggestions": "57"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["58", "59"], ["60", "61"], ["62", "63"], ["64", "65"], ["66", "67"], ["68", "69"], {"messageId": "70", "fix": "71", "desc": "72"}, {"messageId": "73", "fix": "74", "desc": "75"}, {"messageId": "70", "fix": "76", "desc": "72"}, {"messageId": "73", "fix": "77", "desc": "75"}, {"messageId": "70", "fix": "78", "desc": "72"}, {"messageId": "73", "fix": "79", "desc": "75"}, {"messageId": "70", "fix": "80", "desc": "72"}, {"messageId": "73", "fix": "81", "desc": "75"}, {"messageId": "70", "fix": "82", "desc": "72"}, {"messageId": "73", "fix": "83", "desc": "75"}, {"messageId": "70", "fix": "84", "desc": "72"}, {"messageId": "73", "fix": "85", "desc": "75"}, "suggestUnknown", {"range": "86", "text": "87"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "88", "text": "89"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "90", "text": "87"}, {"range": "91", "text": "89"}, {"range": "92", "text": "87"}, {"range": "93", "text": "89"}, {"range": "94", "text": "87"}, {"range": "95", "text": "89"}, {"range": "96", "text": "87"}, {"range": "97", "text": "89"}, {"range": "98", "text": "87"}, {"range": "99", "text": "89"}, [1612, 1615], "unknown", [1612, 1615], "never", [1880, 1883], [1880, 1883], [4600, 4603], [4600, 4603], [4610, 4613], [4610, 4613], [4938, 4941], [4938, 4941], [4948, 4951], [4948, 4951]]