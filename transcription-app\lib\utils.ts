import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { 
  SUPPORTED_FORMATS, 
  FORMAT_EXTENSIONS, 
  MAX_FILE_SIZE,
  TOKEN_RATES,
  PRICING 
} from './constants'
import type { FileValidationError } from '@/types'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format duration in human readable format
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * Validate uploaded file
 */
export function validateFile(file: File): FileValidationError | null {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      type: 'size',
      message: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size of ${formatFileSize(MAX_FILE_SIZE)}`,
      maxAllowed: MAX_FILE_SIZE,
      actual: file.size,
    }
  }
  
  // Check file format
  const isValidMimeType = SUPPORTED_FORMATS.includes(file.type as typeof SUPPORTED_FORMATS[number])
  if (!isValidMimeType) {
    // Also check by file extension as fallback
    const extension = '.' + file.name.split('.').pop()?.toLowerCase()
    const isValidExtension = Object.values(FORMAT_EXTENSIONS).some(
      (extensions: readonly string[]) => extension && extensions.includes(extension)
    )
    
    if (!isValidExtension) {
      return {
        type: 'format',
        message: `File format "${file.type || extension}" is not supported`,
      }
    }
  }
  
  return null
}

/**
 * Get file type category (audio or video)
 */
export function getFileCategory(mimeType: string): 'audio' | 'video' | 'unknown' {
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.startsWith('video/')) return 'video'
  return 'unknown'
}

/**
 * Estimate token count for a file
 */
export function estimateTokenCount(fileSizeBytes: number, durationSeconds: number, mimeType: string): number {
  const category = getFileCategory(mimeType)
  
  if (category === 'audio') {
    return Math.ceil(durationSeconds * TOKEN_RATES.AUDIO_TOKENS_PER_SECOND)
  } else if (category === 'video') {
    return Math.ceil(durationSeconds * TOKEN_RATES.VIDEO_TOKENS_PER_SECOND)
  }
  
  // Fallback estimation based on file size
  return Math.ceil(fileSizeBytes / 1000) // Rough estimate
}

/**
 * Calculate estimated cost for transcription
 */
export function estimateCost(
  tokenCount: number, 
  model: keyof typeof PRICING, 
  mimeType: string
): { input: number; output: number; total: number } {
  const category = getFileCategory(mimeType)
  const pricing = PRICING[model]
  
  let inputCostPer1M: number
  if (category === 'audio') {
    inputCostPer1M = pricing.input.audio
  } else if (category === 'video') {
    inputCostPer1M = pricing.input.video
  } else {
    inputCostPer1M = pricing.input.text
  }
  
  const inputCost = (tokenCount / 1_000_000) * inputCostPer1M
  // Estimate output tokens as 10% of input for transcription
  const outputTokens = tokenCount * 0.1
  const outputCost = (outputTokens / 1_000_000) * pricing.output
  
  return {
    input: inputCost,
    output: outputCost,
    total: inputCost + outputCost,
  }
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

/**
 * Calculate ETA based on progress
 */
export function calculateETA(startTime: Date, progress: number): number | null {
  if (progress <= 0) return null
  
  const elapsed = Date.now() - startTime.getTime()
  const totalEstimated = elapsed / (progress / 100)
  const remaining = totalEstimated - elapsed
  
  return Math.max(0, Math.ceil(remaining / 1000)) // Return seconds
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Convert seconds to SRT timestamp format
 */
export function secondsToSRTTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

/**
 * Convert seconds to VTT timestamp format
 */
export function secondsToVTTTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

/**
 * Sleep utility for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
